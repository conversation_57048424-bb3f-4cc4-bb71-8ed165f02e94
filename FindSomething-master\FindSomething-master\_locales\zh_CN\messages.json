{"extName": {"message": "FindSomething", "description": "扩展名"}, "extDescription": {"message": "在网页的源代码或js中找到一些有趣的东西", "description": "扩展描述"}, "Zhuye": {"message": "主页", "description": "popup页面的主页"}, "Peizhi": {"message": "配置", "description": "popup页面的配置"}, "popupCopy": {"message": "复制", "description": "popup页面的复制按钮"}, "popupCopyurl": {"message": "复制URL", "description": "popup页面的复制URL按钮"}, "popupIp": {"message": "IP", "description": "popup页面的标题：IP"}, "popupIpPort": {"message": "IP加端口", "description": "popup页面的标题：IP_PORT"}, "popupDomain": {"message": "域名", "description": "popup页面的标题：域名"}, "popupSfz": {"message": "身份证", "description": "popup页面的标题：身份证"}, "popupMobile": {"message": "手机号", "description": "popup页面的标题：手机号"}, "popupMail": {"message": "邮箱", "description": "popup页面的标题：邮箱"}, "popupJwt": {"message": "JWT", "description": "popup页面的标题：JWT"}, "popupAlgorithm": {"message": "算法", "description": "popup页面的标题：算法"}, "popupSecret": {"message": "敏感信息", "description": "popup页面的标题：敏感信息"}, "popupPath": {"message": "PATH", "description": "popup页面的标题：路径"}, "popupIncompletePath": {"message": "IncompletePath", "description": "popup页面的标题：不完整的路径"}, "popupUrl": {"message": "URL", "description": "popup页面的标题：url"}, "popupStaticPath": {"message": "StaticPath", "description": "popup页面的标题：静态路径"}, "popupProcessing": {"message": "处理中..", "description": "popup页面的进度提示：处理中"}, "popupComplete": {"message": "处理完成：", "description": "popup页面的进度提示：处理完成"}, "popupTipClickBeforeCopy": {"message": "请点击原页面后再复制：）", "description": "popup页面的点击复制时异常的提示：请点击原页面后再复制：）"}, "settingClearCache": {"message": "清理缓存", "description": "配置页的标题：清理缓存"}, "settingClearLocalStorage": {"message": "清理", "description": "配置页的按钮：清理"}, "settingClearComplete": {"message": "清理完成", "description": "配置页的提示：清理完成"}, "settingClosed": {"message": "已关闭", "description": "配置页的按钮：已关闭"}, "settingOpened": {"message": "已打开", "description": "配置页的按钮：已打开"}, "settingGlobalFloatingWindow": {"message": "全局悬浮窗", "description": "配置页的标题：全局悬浮窗"}, "settingAutoTimeout": {"message": "自动超时", "description": "配置页的标题：自动超时"}, "settingSafe": {"message": "安全模式", "description": "配置页的标题：安全模式"}, "settingWebhook": {"message": "Webhook", "description": "配置页的标题：Webhook"}, "settingWebhookUrl": {"message": "回调地址", "description": "配置页的配置项：回调地址"}, "settingWebhookMethod": {"message": "请求方法", "description": "配置页的配置项：请求方法"}, "settingWebhookArg": {"message": "请求参数", "description": "配置页的配置项：请求方法"}, "settingWebhookHeaders": {"message": "自定义headers", "description": "配置页的配置项：自定义headers"}, "settingDomainAllowList": {"message": "域名白名单", "description": "配置页的配置项：域名白名单"}, "settingDomainAllowListTip": {"message": "输入域名的结尾部分，以换行分隔，若不配置默认使用.google.com,.amazon.com,portswigger.net", "description": "配置页的域名白名单里的提示"}, "settingResetAndSave": {"message": "置空并保存", "description": "配置页的按钮：置空并保存"}, "settingSave": {"message": "保存", "description": "配置页的按钮：保存"}, "getstartedContent": {"message": "不了解功能你怎么能用得好呢？\n## 主页功能\n* ip、ip+端口、域名、身份证、手机号、邮箱、JWT、加密算法、敏感信息、路径、不完整的路径、完整的url、静态路径等字段匹配与展示。\n* 复制\n    * 每一个区域的复制按钮，可以复制对应区域的内容\n    * 单独的复制URL按钮，可以复制PATH（路径）区域的内容，并在每行前面拼接当前页面的url，补充成为完整的url。\n* 信息来源\n    * 每一个展示出来的信息前有一个橘黄色的<，把鼠标放上去静默2秒可看到当前信息来源自哪个链接。你也可以通过“ctrl+左键”或“右键-在新标签页中打开链接”来打开该信息的来源链接。\n* 处理进度展示\n    * 在“主页”下方展示处理进度，如处理中：1/20，这里的1是已请求的链接数，20是要请求的链接总数。如果全部请求完成将展示为处理完成：20/20。\n    * 整个页面的刷新逻辑，也是基于处理进度进行刷新的，在处理完成前通过监听数据变动刷新数据。\n## 配置页功能\n* 清理缓存\n    * 因为提取到的数据都是存储在浏览器的localstorage里的，如果长时间不清理，可能会占用机器内存。\n    * 插件在2.0.17版本新增了自动过期逻辑，7天未访问的数据将会过期。\n    * 用户也可以手动点击“清理”，可以立即清空存储的数据。\n* 全局悬浮窗\n    * 打开后FindSomething的页面就会嵌入到每个打开的页面里，省去点开插件的交互。\n    * 但是目前没有把新的功能加到全局悬浮窗里，仅保持基础功能可用。\n* 自动超时\n    * 默认关闭的配置，打开后在插件后台发起的请求将会在2s后被终止，避免长时间等待。\n* 安全模式\n    * 在安全模式下插件仅会访问js资源，以避免退出当前页面或请求到敏感的操作链接。\n    * 在关闭安全模式后，会访问当前页面src、href属性的全部url，不区分链接类型，包括css、图片或是api等，这可能会导致出现预期外的请求。\n    * 安全模式默认为打开状态。\n* Webhook回调\n    * 我建议使用post，因为get的参数有长度限制。\n    * 如果请求参数位置写了参数名，那么请求体就是data={“xx”:”xx”}这种格式。\n    * 如果请求参数位置没有写参数名，那么请求体会是json格式。\n    * 请求体里有包括配置、进度、提取到的全部信息和对应的来源。\n    * 有些网站提取到的内容特别多，需要注意配置你的webhook站点能在一个请求中接受这么多数据。\n* 域名白名单\n    * 判定逻辑是以xxx结尾，在白名单的域名不会请求。", "description": "getstarted"}}
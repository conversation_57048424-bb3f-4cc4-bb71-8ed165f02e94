{"extName": {"message": "FindSomething", "description": "Extension name"}, "extDescription": {"message": "Find interesting things in the webpage's source code or JavaScript", "description": "Extension description"}, "Zhuye": {"message": "Home", "description": "Home page of the popup"}, "Peizhi": {"message": "Settings", "description": "Settings page of the popup"}, "popupCopy": {"message": "Copy", "description": "Copy button on the popup page"}, "popupCopyurl": {"message": "Copy URL", "description": "Copy URL button on the popup page"}, "popupIp": {"message": "IP", "description": "Title on the popup page: IP"}, "popupIpPort": {"message": "IP and Port", "description": "Title on the popup page: IP_PORT"}, "popupDomain": {"message": "Domain", "description": "Title on the popup page: Domain"}, "popupSfz": {"message": "ID Card", "description": "Title on the popup page: ID Card"}, "popupMobile": {"message": "Mobile Number", "description": "Title on the popup page: Mobile Number"}, "popupMail": {"message": "Email", "description": "Title on the popup page: <PERSON><PERSON>"}, "popupJwt": {"message": "JWT", "description": "Title on the popup page: JWT"}, "popupAlgorithm": {"message": "Algorithm", "description": "Title on the popup page: Algorithm"}, "popupSecret": {"message": "Sensitive Information", "description": "Title on the popup page: Sensitive Information"}, "popupPath": {"message": "PATH", "description": "Title on the popup page: Path"}, "popupIncompletePath": {"message": "Incomplete Path", "description": "Title on the popup page: Incomplete Path"}, "popupUrl": {"message": "URL", "description": "Title on the popup page: URL"}, "popupStaticPath": {"message": "Static Path", "description": "Title on the popup page: Static Path"}, "popupProcessing": {"message": "Processing...", "description": "Progress prompt on the popup page: Processing"}, "popupComplete": {"message": "Processing Complete:", "description": "Progress prompt on the popup page: Processing Complete"}, "popupTipClickBeforeCopy": {"message": "Please click on the original page before copying :)", "description": "Copy tip on the popup page when an error occurs"}, "settingClearCache": {"message": "<PERSON>ache", "description": "Title on the settings page: Clear Cache"}, "settingClearLocalStorage": {"message": "Clear", "description": "But<PERSON> on the settings page: Clear"}, "settingClearComplete": {"message": "Clear Complete", "description": "Prompt on the settings page: Clear Complete"}, "settingClosed": {"message": "Closed", "description": "<PERSON><PERSON> on the settings page: Closed"}, "settingOpened": {"message": "Opened", "description": "Button on the settings page: Opened"}, "settingGlobalFloatingWindow": {"message": "Global Floating Window", "description": "Title on the settings page: Global Floating Window"}, "settingAutoTimeout": {"message": "Auto Timeout", "description": "Title on the settings page: Auto Timeout"}, "settingSafe": {"message": "Safe Mode", "description": "Title on the settings page: Safe Mode"}, "settingWebhook": {"message": "Webhook", "description": "Title on the settings page: Webhook"}, "settingWebhookUrl": {"message": "Callback URL", "description": "Setting item on the settings page: Callback URL"}, "settingWebhookMethod": {"message": "Request Method", "description": "Setting item on the settings page: Request Method"}, "settingWebhookArg": {"message": "Request Parameters", "description": "Setting item on the settings page: Request Parameters"}, "settingWebhookHeaders": {"message": "Custom Headers", "description": "Setting item on the settings page: Custom Headers"}, "settingDomainAllowList": {"message": "Domain Allowlist", "description": "Setting item on the settings page: Domain Allowlist"}, "settingDomainAllowListTip": {"message": "Enter the end part of the domain, separated by new lines, if not configured defaults to .google.com, .amazon.com, portswigger.net", "description": "Tip in the settings page for Domain Allowlist"}, "settingResetAndSave": {"message": "Reset and Save", "description": "But<PERSON> on the settings page: Reset and Save"}, "settingSave": {"message": "Save", "description": "<PERSON><PERSON> on the settings page: Save"}, "getstartedContent": {"message": "How can you use it well if you don't understand the features?\n## Home Page Features\n* Field matching and display for IP, IP+port, domain, ID card number, phone number, email, JWT, encryption algorithms, sensitive information, path, incomplete path, full URL, static path, etc.\n* Copy\n    * A copy button for each area, which can copy the content of the corresponding area\n    * A separate \"Copy URL\" button that can copy the content of the PATH area and prepend the current page's URL to each line to form a complete URL.\n* Information Source\n    * Each displayed information is preceded by an orange '<'. Hover your mouse over it and stay silent for 2 seconds to see the source URL of the current information. You can also open the source link by \"ctrl+click\" or \"right-click - open link in new tab\".\n* Progress Display\n    * The processing progress is displayed under the \"Home\" page, e.g., Processing: 1/20, where 1 is the number of links requested and 20 is the total number of links to be requested. If all requests are completed, it will display as Completed: 20/20.\n    * The entire page's refresh logic is also based on processing progress, refreshing data by listening for data changes before processing is complete.\n## Configuration Page Features\n* Clear Cache\n    * Since the extracted data is stored in the browser's localstorage, it can occupy memory if not cleared for a long time.\n    * The plugin introduced an automatic expiration logic in version 2.0.17, where data not accessed within 7 days will expire.\n    * Users can also manually click \"Clear\" to immediately clear the stored data.\n* Global Floating Window\n    * Once opened, the FindSomething page will embed into every opened page, eliminating the need to interact with the plugin.\n    * However, new features have not yet been added to the global floating window, only maintaining basic functionality.\n* Auto Timeout\n    * This setting is off by default. When enabled, requests initiated by the plugin backend will be terminated after 2s to avoid long waiting times.\n* Safe Mode\n    * In safe mode, the plugin will only access JavaScript resources to avoid leaving the current page or accessing sensitive operation links.\n    * When safe mode is turned off, it will access all URLs of the current page's src and href attributes, regardless of link type, including CSS, images, or APIs, which may lead to unexpected requests.\n    * Safe mode is enabled by default.\n* Webhook Callback\n    * I recommend using POST, as GET has limitations on the length of parameters.\n    * If the parameter name is written in the request parameter location, then the request body will be in the format data={\"xx\":\"xx\"}.\n    * If no parameter name is written in the request parameter location, then the request body will be in JSON format.\n    * The request body includes configuration, progress, all extracted information, and their sources.\n    * Some websites extract a lot of content, so be mindful of configuring your webhook site to handle such a large amount of data in one request.\n* Domain Whitelist\n    * The logic is based on ending with xxx. Domains on the whitelist will not be requested.", "description": "getstarted"}}
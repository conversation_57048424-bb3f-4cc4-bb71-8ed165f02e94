# 变更日志
此项目的所有显著更改将记录在此文件中。
## [2.0.19]-2024-11-10
感谢1MurasaKi对自动退出bug的反馈。
### 添加
- 新增了国际化语言支持
- 新增了“安全模式”，在安全模式下插件仅会访问js资源，以避免退出当前页面；在关闭安全模式后，会访问当前页面src、href属性的全部url，不区分链接类型，包括css、图片或是api等，这可能会导致出现预期外的请求。安全模式默认为打开状态。
- 新增了安装后自动弹出的功能介绍。
### 变更
- 优化了前端数据更新的逻辑，会减少插件的性能占用。
### 修复
- 修复了会自动退出的bug，原因是会提取一些链接进行访问，而判断链接为js的逻辑存在缺陷，现在已经修复了这个问题。

## [2.0.18]-2024-05-16
感谢 天明、 cloud-jie 针对WebInfoHunter的建议。
感谢 cloud-jie 对iframe 问题的反馈。
### 添加
- 新增了对 iframe 页面的检索。
- 新增了ARL项目中WebInfoHunter工具的部分敏感信息的正则，感谢ARL项目。

## [2.0.17]-2024-01-13
感谢 喂草。、 向上 对内存占用问题的反馈。
感谢 FREE2E! 对.jsp后缀错误分拣到静态路径区问题的反馈。
### 变更
- 因为旧版本收集到的数据会永久存在localStorage中，导致在每次存取数据时出现内存暴增的问题。本次增加了对数据的过期逻辑，7天未访问的url数据将会过期。
- 配置页新增了“清除缓存”操作，用户可手动清除缓存。
- 补充了.tw的顶级域。

### 修复
- 修复了对.jsp后缀错误分拣到静态路径区的问题。

## [2.0.16]-2023-07-09
感谢 长风 对webhook回传信息不全问题的反馈。
### 变更
- 优化了后台的刷新结构。
### 修复
- 修复了webhook回传信息不全的问题。

## [2.0.15]-2023-06-26
感谢 独自等待、 Wild Code Developer、 MiaoTony、 KexinCC 对无法登录问题的反馈。
感谢 Xc1Ym 对白名单问题的反馈。
### 修复
- 修复部分网站无法登录的问题。导致这个问题的原因是打开页面后插件会再次请求页面，这会导致一次性的token失效。现在通过直接使用页面源码提取信息代替了再次请求。
- 修复了白名单的bug，现在白名单能以域名(IP)或域名(IP)+端口维度进行限制，如a.com可以认为对sub.a.com、a.com:8001都生效。
- 修复了StaticUrl的重复问题。

## [2.0.14]-2023-03-15
感谢 hyasin 对复制字符体验的反馈
### 变更
- 优化了信息来源提示改到 < 图标上，以优化信息的复制体验。
- 优化了IP、域名信息提取逻辑，从已提取url中继续提取相关联的信息。
### 修复
- 修复白名单设置后展示的bug。

## [2.0.13]-2023-03-07
感谢 osxtest 为本项目贡献代码
感谢 冰茶、 明月清风、 不悲、 1nfosec、 潜龙在渊 等师傅对来源提示和超链接功能的建议
感谢 独自等待、 Wild Code Developer 对登录异常的反馈
### 添加
- 新增了插件图标处有几种类型被匹配到的数字提示，此功能为 osxtest 开发。
- 新增了域名白名单，通过配置常用的域名，避免目标站点登录时csrf_token失效。
- 新增了提取到的信息来源提示和超链接，现在你可以通过“ctrl+左键”或“右键-在新标签页中打开链接”来打开该信息的来源链接。
### 修复
- 修复一些体验上的bug。

## [2.0.12]-2023-02-14
感谢 小笼包 提出bug。
### 修复
- 修复当前页面无script标签时无法正常使用的bug。
- 修复了一些空值判断导致的异常。

## [2.0.11]-2022-11-09
感谢 xiaoliangli1128 提出bug，并提供了部分规则。
### 添加
- 新增了几个敏感信息提取规则。
### 修复
- 修复了敏感信息提取不完整的bug，因为使用了重复的变量导致这个bug。

## [2.0.10]-2022-08-27
### 修复
- 修复了2.0.9全局悬浮窗无法复制的bug

## [2.0.9]-2022-08-26
### 添加
- 新增了自动超时配置，打开后请求在2s后无响应时将被中止，避免长时间等待。
- 新增了复制Path时补全url的功能。
### 变更
- 调整了全局悬浮窗的默认高度，之前似乎太长了。
- 另外，我在尝试通过监听响应来避免再次主动网络请求，但发现webrequest并没有提供查看响应body的方法。我看到其他的方法是监听xhr，但似乎无法覆盖全部流量，并且结果无法写入到插件的localstorage，这让我依旧保留了当前的请求方式。
### 修复
- 修复了后台脚本停止运行后数据消失的问题，通过将提取到的数据存储到localstorage的方式。
- 修复了匹配不到带协议和端口号的一些字段。

## [2.0.8]-2022-06-18
感谢mdkk对此版本的建议
感谢NeeSung对UI的建议
### 添加
- 新增了webhook配置页，当后台服务处理完成后，将数据发送到指定api
- 新增了可拖拽的全局悬浮窗，可在配置页控制开关
- 新增了处理进度展示
### 变更
- 将manifest_version升级到了3
- 优化了UI
### 修复
- 尝试修复了部分数据提取不到的问题

## [2.0.7]-2022-05-26
感谢0cat对此版本的建议
### 添加
- 新增了通过关键词提取部分敏感数据，使用了nuclei的规则，感谢开源。https://github.com/projectdiscovery/nuclei-templates/blob/master/exposures/tokens/generic/credentials-disclosure.yaml
- 新增了复制功能，一键复制该区域结果
- 新增了非标准开头的路径提取，这将会存在一些误报。如 api/path
### 变更
- 将提取部分移到背景页中，避免前端加载页面时出现阻塞
### 修复
- 尝试修复了部分数据提取不到的问题

## [2.0.6]-2022-05-03
### 变更
- 优化正则，path增加./和../格式，url增加端口格式匹配

## [2.0.5]-2022-01-07
### 变更
- 提取链接的正则添加了单引号
- 踢出非js链接时，保留script标签下非`.js`结尾的链接

## [2.0.1]-2021-06-26
### 添加
- 新增了加密/签名算法函数的匹配，对应算法与关键字为base64(base64、btoa、atob)、aes(CryptoJS.AES)、des(CryptoJS.DES)、rsa(JSEncrypt、rsa、KJUR)、md5(md5)、sha1(sha1)、sha256(sha256)、sha512(sha512)。
### 变更
- 根据Hae的开源的公共规则更新了身份证、手机号、邮箱、jwt等字段的正则表达式，非常感谢开源的公共规则。https://gh0st.cn/HaE/

## [2.0.0]-2020-09-12
### 添加
- ip、ip+端口、域名、路径、url、静态路径、身份证、手机号、邮箱等字段匹配与展示
